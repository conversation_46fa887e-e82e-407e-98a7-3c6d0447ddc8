import { expect } from '@playwright/test';
import { v4 as uuidv4 } from 'uuid';
import { test } from '../../fixtures/default-fixture';
import { EnrollmentsRepo } from '../../../shared/repositories/lms/repo/enrollments-repo/enrollments-repo';
import { EnrollType } from '../../../shared/repositories/lms/constants/enums/enroll-type.enum';

let resetTimestamp: number;

test.describe('Admin - Manage enrollment status', () => {
  test.beforeEach(
    async ({
      roundRepo,
      usersRepo,
      enrollmentsRepo,
      verifyEnrollmentRepo,
      enrollmentCertificatesRepo,
      configuration,
    }) => {
      const course = configuration.shareCourses.oicCourseManualApprove;
      const roundDate = new Date();
      roundDate.setHours(0, 0, 0, 0);

      const lmsUser = await usersRepo.getByEmail(configuration.shareUsers.userCommentTLI.email);
      const userEnrollments = await enrollmentsRepo.getAllEnrollmentsForUser(lmsUser.guid);

      //Clean up round
      await roundRepo.deleteTrainingRoundByTrainingDateAndCourseId(course.courseId, roundDate);

      //Clean up enrollment
      for (const userEnrollment of userEnrollments) {
        await enrollmentCertificatesRepo.deleteAllEnrollmentCertificatesForEnrollment(userEnrollment.id);
        await verifyEnrollmentRepo.deleteManyByEnrollmentId(userEnrollment.id);
      }
      await enrollmentsRepo.deleteAllEnrollmentsForUser(lmsUser.guid);
      resetTimestamp = new Date().getTime();
    },
  );

  test.afterEach(
    async ({
      roundRepo,
      usersRepo,
      enrollmentsRepo,
      enrollmentCertificatesRepo,
      verifyEnrollmentRepo,
      configuration,
    }) => {
      const course = configuration.shareCourses.oicCourseManualApprove;
      const roundDate = new Date();
      roundDate.setHours(0, 0, 0, 0);

      const lmsUser = await usersRepo.getByEmail(configuration.shareUsers.userCommentTLI.email);
      const userEnrollments = await enrollmentsRepo.getAllEnrollmentsForUser(lmsUser.guid);

      //Clean up round
      await roundRepo.deleteTrainingRoundByTrainingDateAndCourseId(course.courseId, roundDate);

      //Clean up enrollment & verify enrollment data
      for (const userEnrollment of userEnrollments) {
        await enrollmentCertificatesRepo.deleteAllEnrollmentCertificatesForEnrollment(userEnrollment.id);
        await verifyEnrollmentRepo.deleteManyByEnrollmentId(userEnrollment.id);
      }
      await enrollmentsRepo.deleteAllEnrollmentsForUser(lmsUser.guid);
      resetTimestamp = new Date().getTime();
    },
  );
  
  test('@SKL-T20082 @SKL-T20084 Admin verify and approve enrollment', async ({
    page,
    configuration,
    loginPage,
    homePage,
    myCoursePage,
    learningPage,
    adminDashboardPage,
    verifyIdentityPage,
    pendingApprovalPage,
    pendingApprovalDetailPage,
    enrollmentsRepo,
    roundRepo,
  }) => {
    const course = configuration.shareCourses.oicCourseManualApprove;
    const courseVersion = configuration.shareCourses.oicCourseManualApprove.courseVersions[1];
    const userTLI = configuration.shareUsers.userCommentTLI;
    const organizationAdminTLI = configuration.shareUsers.userAdminReplyTLI;

    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);

    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    // Seed round
    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: date,
      updatedAt: date,
      courseIds: [course.courseId],
      organizationId: userTLI.organizationId,
    });

    // Enrollment OIC course to user
    const enrollment = await enrollmentsRepo.create({
      id: uuidv4(),
      courseId: course.courseId,
      courseVersionId: courseVersion.id,
      organizationId: userTLI.organizationId,
      userId: userTLI.guid,
      business: EnrollmentsRepo.BUSINESS.b2c,
      status: EnrollmentsRepo.STATUS.inProgress,
      isCountdownArticle: false,
      roundId: round.id,
      completedCourseItem: 0,
      learningProgress: [],
      startedAt: date,
      expiredAt: expiredAt,
      acceptedAt: null,
      createdAt: date,
      updatedAt: date,
      isIdentityVerificationEnabled: false,
      remark: '',
      externalContentType: '',
      enrollType: EnrollType.voluntary,
      customerCode: '',
    });

    //User learn and submit course
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await myCoursePage.continueLearning(1);
    await verifyIdentityPage.submitAgreeConsent();
    await learningPage.finishOICCourseModalLocator.waitFor({ state: 'visible' });
    await learningPage.submitLearningResultOnModal();
    await myCoursePage.waitForCourseLoaded();
    await page.waitForLoadState('load');
    expect(await myCoursePage.verifyCourseStatusByCourseName(course.name)).toContain('รออนุมัติ');
    await homePage.logout();

    // Admin verify and approve enrollment
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessManagePendingApprovalOIC();
    await pendingApprovalPage.searchPendingApproval(`${userTLI.firstname} ${userTLI.lastname}`);
    await pendingApprovalDetailPage.accessPendingApprovalDetail();
    await pendingApprovalDetailPage.pendingApprovalElement.submitVerified();
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await pendingApprovalDetailPage.approvalHistoryTab.click();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('ผ่านการตรวจสอบ');
    await expect(pendingApprovalDetailPage.latestApprovalHistoryReasonLocator).toContainText('-');
    await homePage.logout();

    //Learner verify pending approval status
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await page.waitForLoadState('load');
    expect(await myCoursePage.verifyCourseStatusByCourseName(course.name)).toContain('รออนุมัติ');
    await homePage.logout();

    //Admin approve enrollment
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessManagePendingApprovalOIC();
    await pendingApprovalPage.searchPendingApproval(`${userTLI.firstname} ${userTLI.lastname}`);
    await pendingApprovalPage.filterStatus('ผ่านการตรวจสอบ');
    await pendingApprovalDetailPage.accessPendingApprovalDetail();
    await pendingApprovalDetailPage.pendingApprovalElement.submitApproval();
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await pendingApprovalDetailPage.approvalHistoryTab.click();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('อนุมัติ');
    await expect(pendingApprovalDetailPage.latestApprovalHistoryReasonLocator).toContainText('-');
    await homePage.logout();

    //Learner verify approve status
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await myCoursePage.clickToMyCourse();
    await myCoursePage.checkNoCourseFoundAndClickLearningHistory();
    await myCoursePage.verifyLearningHistoryStatus('การอบรมวิชาชีพประกัน (OIC)', courseVersion.name, 'อนุมัติ');
  });
  
  test('@SKL-T20081 @SKL-T20083 Admin revert status from verify to pending approval and reject enrollment', async ({
    configuration,
    loginPage,
    homePage,
    myCoursePage,
    learningPage,
    adminDashboardPage,
    verifyIdentityPage,
    pendingApprovalPage,
    pendingApprovalDetailPage,
    enrollmentsRepo,
    roundRepo,
    page,
  }) => {
    const course = configuration.shareCourses.oicCourseManualApprove;
    const courseVersion = configuration.shareCourses.oicCourseManualApprove.courseVersions[1];
    const userTLI = configuration.shareUsers.userCommentTLI;
    const organizationAdminTLI = configuration.shareUsers.userAdminReplyTLI;

    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);

    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    // Seed round
    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: date,
      updatedAt: date,
      courseIds: [course.courseId],
      organizationId: userTLI.organizationId,
    });

    // Enrollment OIC course to user
    const enrollment = await enrollmentsRepo.create({
      id: uuidv4(),
      courseId: course.courseId,
      courseVersionId: courseVersion.id,
      organizationId: userTLI.organizationId,
      userId: userTLI.guid,
      business: EnrollmentsRepo.BUSINESS.b2c,
      status: EnrollmentsRepo.STATUS.inProgress,
      isCountdownArticle: false,
      roundId: round.id,
      completedCourseItem: 0,
      learningProgress: [],
      startedAt: date,
      expiredAt: expiredAt,
      acceptedAt: null,
      createdAt: date,
      updatedAt: date,
      isIdentityVerificationEnabled: false,
      remark: '',
      externalContentType: '',
      enrollType: EnrollType.voluntary,
      customerCode: '',
    });

    //User learn and submit course
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await myCoursePage.continueLearning(1);
    await verifyIdentityPage.submitAgreeConsent();
    await learningPage.finishOICCourseModalLocator.waitFor({ state: 'visible' });
    await learningPage.submitLearningResultOnModal();
    await myCoursePage.waitForCourseLoaded();
    expect(await myCoursePage.verifyCourseStatusByCourseName(course.name)).toContain('รออนุมัติ');
    await homePage.logout();

    // Admin back to verify and reject enrollment
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessManagePendingApprovalOIC();
    await pendingApprovalPage.searchPendingApproval(userTLI.firstname);
    await pendingApprovalDetailPage.accessPendingApprovalDetail();
    await pendingApprovalDetailPage.pendingApprovalElement.submitVerified();
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await pendingApprovalDetailPage.approvalHistoryTab.click();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('ผ่านการตรวจสอบ');
    await expect(pendingApprovalDetailPage.latestApprovalHistoryReasonLocator).toContainText('-');

    await pendingApprovalDetailPage.enrollmentDetailTab.click();
    await pendingApprovalDetailPage.pendingApprovalElement.backToVerify();
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await pendingApprovalDetailPage.approvalHistoryTab.click();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('กลับไปตรวจสอบ	');
    await expect(pendingApprovalDetailPage.latestApprovalHistoryReasonLocator).toContainText('-');
    await homePage.logout();

    //Learner verify pending approval status
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await page.waitForLoadState('load');
    expect(await myCoursePage.verifyCourseStatusByCourseName(course.name)).toContain('รออนุมัติ');
    await homePage.logout();

    //Admin reject enrollment
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessManagePendingApprovalOIC();
    await pendingApprovalPage.searchPendingApproval(userTLI.firstname);
    await pendingApprovalDetailPage.accessPendingApprovalDetail();
    await pendingApprovalDetailPage.pendingApprovalElement.submitReject('เอกสารไม่ครบ');
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await pendingApprovalDetailPage.approvalHistoryTab.click();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('ไม่อนุมัติ');
    await expect(pendingApprovalDetailPage.latestApprovalHistoryReasonLocator).toContainText('เอกสารไม่ครบ');
    await homePage.logout();

    //Learner verify reject status
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await myCoursePage.clickToMyCourse();
    await myCoursePage.checkNoCourseFoundAndClickLearningHistory();
    await myCoursePage.verifyLearningHistoryStatus('การอบรมวิชาชีพประกัน (OIC)', courseVersion.name, 'ไม่อนุมัติ');
  });
});
